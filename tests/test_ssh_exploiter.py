"""
Tests for SSH Exploitation Module
"""

import pytest
import asyncio
import tempfile
import sys
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from pathlib import Path
from datetime import datetime

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from exploits.ssh_exploiter import SSHExploiter, SSHCredentials, SSHSession
from exploits.multi_protocol_engine import ExploitTarget, ExploitResult, ExploitationConfig, ExploitationMode, ProtocolType
from core.config import ConfigManager


class TestSSHCredentials:
    """Test cases for SSHCredentials dataclass"""

    def test_password_credentials(self):
        """Test password-based credentials"""
        creds = SSHCredentials(username="admin", password="password123")
        assert creds.username == "admin"
        assert creds.password == "password123"
        assert creds.private_key_path is None
        assert creds.private_key_data is None
        assert creds.passphrase is None

    def test_key_credentials(self):
        """Test key-based credentials"""
        creds = SSHCredentials(
            username="root",
            private_key_path="/home/<USER>/.ssh/id_rsa",
            passphrase="keypass"
        )
        assert creds.username == "root"
        assert creds.password is None
        assert creds.private_key_path == "/home/<USER>/.ssh/id_rsa"
        assert creds.passphrase == "keypass"


class TestSSHSession:
    """Test cases for SSHSession dataclass"""

    def test_session_creation(self):
        """Test SSH session creation"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        mock_client = Mock()
        creds = SSHCredentials(username="admin", password="password123")

        session = SSHSession(target=target, client=mock_client, credentials=creds)

        assert session.target == target
        assert session.client == mock_client
        assert session.credentials == creds
        assert isinstance(session.established_at, datetime)
        assert isinstance(session.last_activity, datetime)
        assert session.tunnels == []


class TestSSHExploiter:
    """Test cases for SSHExploiter class"""

    def setup_method(self):
        """Setup test environment"""
        self.config_manager = Mock(spec=ConfigManager)
        self.config_manager.get.return_value = 100  # max_attempts default
        self.exploiter = SSHExploiter(self.config_manager)

    def test_exploiter_initialization(self):
        """Test SSH exploiter initialization"""
        assert self.exploiter.config_manager == self.config_manager
        assert isinstance(self.exploiter.default_usernames, list)
        assert isinstance(self.exploiter.default_passwords, list)
        assert len(self.exploiter.default_usernames) > 0
        assert len(self.exploiter.default_passwords) > 0
        assert self.exploiter.active_sessions == {}

    def test_default_credentials_lists(self):
        """Test default username and password lists"""
        # Check that common usernames are included
        assert "admin" in self.exploiter.default_usernames
        assert "root" in self.exploiter.default_usernames
        assert "user" in self.exploiter.default_usernames
        
        # Check that common passwords are included
        assert "password" in self.exploiter.default_passwords
        assert "admin" in self.exploiter.default_passwords
        assert "123456" in self.exploiter.default_passwords

    @pytest.mark.asyncio
    async def test_load_wordlist_success(self):
        """Test successful wordlist loading"""
        # Create temporary wordlist file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as f:
            f.write("user1\nuser2\nuser3\n\n# comment\nuser4\n")
            temp_path = f.name

        try:
            wordlist = await self.exploiter._load_wordlist(temp_path)
            # The wordlist loader filters out empty lines but not comments
            assert "user1" in wordlist
            assert "user2" in wordlist
            assert "user3" in wordlist
            assert "user4" in wordlist
            assert len(wordlist) == 5  # Including the comment line
        finally:
            Path(temp_path).unlink()

    @pytest.mark.asyncio
    async def test_load_wordlist_nonexistent_file(self):
        """Test loading non-existent wordlist file"""
        wordlist = await self.exploiter._load_wordlist("/nonexistent/path.txt")
        assert wordlist == []

    @pytest.mark.asyncio
    async def test_load_wordlist_none_path(self):
        """Test loading wordlist with None path"""
        wordlist = await self.exploiter._load_wordlist(None)
        assert wordlist == []

    @pytest.mark.asyncio
    async def test_try_ssh_login_success(self):
        """Test successful SSH login attempt"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")

        with patch('paramiko.SSHClient') as mock_ssh_class:
            mock_client = Mock()
            mock_ssh_class.return_value = mock_client
            mock_client.connect = Mock()

            with patch('asyncio.wait_for') as mock_wait_for:
                with patch('asyncio.to_thread') as mock_to_thread:
                    mock_wait_for.return_value = None  # Successful connection

                    success, client = await self.exploiter._try_ssh_login(
                        target, "admin", "password", 30
                    )

                    assert success is True
                    assert client == mock_client
                    mock_client.set_missing_host_key_policy.assert_called_once()

    @pytest.mark.asyncio
    async def test_try_ssh_login_failure(self):
        """Test failed SSH login attempt"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")

        with patch('paramiko.SSHClient') as mock_ssh_class:
            mock_client = Mock()
            mock_ssh_class.return_value = mock_client

            with patch('asyncio.wait_for') as mock_wait_for:
                mock_wait_for.side_effect = Exception("Connection failed")

                success, client = await self.exploiter._try_ssh_login(
                    target, "admin", "wrongpass", 30
                )

                assert success is False
                assert client is None
                mock_client.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_try_ssh_key_login_success(self):
        """Test successful SSH key login attempt"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")

        with patch('paramiko.SSHClient') as mock_ssh_class:
            mock_client = Mock()
            mock_ssh_class.return_value = mock_client
            mock_client.connect = Mock()

            with patch('asyncio.wait_for') as mock_wait_for:
                with patch('asyncio.to_thread') as mock_to_thread:
                    mock_wait_for.return_value = None  # Successful connection

                    success, client = await self.exploiter._try_ssh_key_login(
                        target, "root", "/path/to/key", 30
                    )

                    assert success is True
                    assert client == mock_client

    @pytest.mark.asyncio
    async def test_brute_force_attack_success(self):
        """Test successful brute force attack"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        config = ExploitationConfig(
            mode=ExploitationMode.BRUTE_FORCE,
            timeout=30,
            wordlist_paths={'users': None, 'passwords': None}
        )

        # Mock successful login on second attempt
        with patch.object(self.exploiter, '_try_ssh_login') as mock_login:
            mock_client = Mock()
            mock_login.side_effect = [
                (False, None),  # First attempt fails
                (True, mock_client)  # Second attempt succeeds
            ]

            result = await self.exploiter._brute_force_attack(target, config)

            assert result.success is True
            assert result.exploit_type == "ssh_brute_force"
            assert "username" in result.credentials
            assert "password" in result.credentials
            assert result.access_level == "user"

    @pytest.mark.asyncio
    async def test_brute_force_attack_failure(self):
        """Test failed brute force attack"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        config = ExploitationConfig(
            mode=ExploitationMode.BRUTE_FORCE,
            timeout=30,
            wordlist_paths={'users': None, 'passwords': None}
        )

        # Mock all login attempts fail
        with patch.object(self.exploiter, '_try_ssh_login') as mock_login:
            mock_login.return_value = (False, None)

            result = await self.exploiter._brute_force_attack(target, config)

            assert result.success is False
            assert result.exploit_type == "ssh_brute_force"
            assert "failed after" in result.error_message

    @pytest.mark.asyncio
    async def test_key_based_attack_success(self):
        """Test successful key-based attack"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Create a temporary key file
        with tempfile.NamedTemporaryFile(delete=False, suffix='_rsa') as f:
            temp_key_path = f.name

        try:
            with patch('pathlib.Path.expanduser') as mock_expand:
                mock_path = Mock()
                mock_path.exists.return_value = True
                mock_expand.return_value = mock_path

                with patch.object(self.exploiter, '_try_ssh_key_login') as mock_key_login:
                    mock_client = Mock()
                    mock_key_login.return_value = (True, mock_client)

                    result = await self.exploiter._key_based_attack(target, config)

                    assert result.success is True
                    assert result.exploit_type == "ssh_key_auth"
                    assert "username" in result.credentials
                    assert "key_path" in result.credentials
        finally:
            Path(temp_key_path).unlink(missing_ok=True)

    @pytest.mark.asyncio
    async def test_default_credentials_attack_success(self):
        """Test successful default credentials attack"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        with patch.object(self.exploiter, '_try_ssh_login') as mock_login:
            mock_client = Mock()
            mock_login.return_value = (True, mock_client)

            result = await self.exploiter._default_credentials_attack(target, config)

            assert result.success is True
            assert result.exploit_type == "ssh_default_creds"
            assert "username" in result.credentials
            assert "password" in result.credentials

    @pytest.mark.asyncio
    async def test_execute_command_success(self):
        """Test successful command execution"""
        # Setup active session
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        mock_client = Mock()
        creds = SSHCredentials(username="admin", password="password")
        session = SSHSession(target=target, client=mock_client, credentials=creds)

        session_key = "*************:22"
        self.exploiter.active_sessions[session_key] = session

        # Mock command execution
        mock_stdin = Mock()
        mock_stdout = Mock()
        mock_stderr = Mock()
        mock_stdout.read.return_value = b"command output"
        mock_stderr.read.return_value = b""

        mock_client.exec_command.return_value = (mock_stdin, mock_stdout, mock_stderr)

        result = await self.exploiter.execute_command(session_key, "ls -la")

        assert result == "command output"
        mock_client.exec_command.assert_called_once_with("ls -la")

    @pytest.mark.asyncio
    async def test_execute_command_no_session(self):
        """Test command execution with no active session"""
        result = await self.exploiter.execute_command("nonexistent:22", "ls")
        assert result is None

    def test_close_session_success(self):
        """Test successful session closure"""
        # Setup active session
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        mock_client = Mock()
        creds = SSHCredentials(username="admin", password="password")
        session = SSHSession(target=target, client=mock_client, credentials=creds)

        session_key = "*************:22"
        self.exploiter.active_sessions[session_key] = session

        result = self.exploiter.close_session(session_key)

        assert result is True
        assert session_key not in self.exploiter.active_sessions
        mock_client.close.assert_called_once()

    def test_close_session_nonexistent(self):
        """Test closing non-existent session"""
        result = self.exploiter.close_session("nonexistent:22")
        assert result is False

    def test_get_active_sessions(self):
        """Test getting active sessions information"""
        # Setup active session
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        mock_client = Mock()
        creds = SSHCredentials(username="admin", password="password")
        session = SSHSession(target=target, client=mock_client, credentials=creds)

        session_key = "*************:22"
        self.exploiter.active_sessions[session_key] = session

        sessions_info = self.exploiter.get_active_sessions()

        assert session_key in sessions_info
        session_info = sessions_info[session_key]
        assert session_info['target']['host'] == "*************"
        assert session_info['target']['port'] == 22
        assert session_info['credentials']['username'] == "admin"
        assert session_info['credentials']['auth_method'] == "password"

    @pytest.mark.asyncio
    async def test_exploit_single_target_success(self):
        """Test successful single target exploitation"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock successful brute force attack
        with patch.object(self.exploiter, '_brute_force_attack') as mock_brute:
            mock_result = ExploitResult(
                target=target,
                success=True,
                exploit_type="ssh_brute_force",
                credentials={'username': 'admin', 'password': 'password'},
                access_level="user"
            )
            mock_brute.return_value = mock_result

            result = await self.exploiter.exploit_single_target(target, config)

            assert result.success is True
            assert result.exploit_type == "ssh_brute_force"
            assert result.execution_time is not None

    @pytest.mark.asyncio
    async def test_exploit_single_target_all_methods_fail(self):
        """Test single target exploitation when all methods fail"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock all attack methods to fail
        failed_result = ExploitResult(
            target=target,
            success=False,
            exploit_type="ssh_test",
            error_message="Attack failed"
        )

        with patch.object(self.exploiter, '_brute_force_attack', return_value=failed_result):
            with patch.object(self.exploiter, '_key_based_attack', return_value=failed_result):
                with patch.object(self.exploiter, '_default_credentials_attack', return_value=failed_result):

                    result = await self.exploiter.exploit_single_target(target, config)

                    assert result.success is False
                    assert result.exploit_type == "ssh_exploitation"
                    assert "All SSH attack methods failed" in result.error_message

    @pytest.mark.asyncio
    async def test_exploit_multiple_targets(self):
        """Test exploiting multiple targets"""
        targets = [
            ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh"),
            ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        ]
        config = ExploitationConfig(
            mode=ExploitationMode.TARGETED,
            timeout=30,
            max_concurrent=2
        )

        # Mock successful exploitation for both targets
        with patch.object(self.exploiter, 'exploit_single_target') as mock_exploit:
            mock_results = [
                ExploitResult(target=targets[0], success=True, exploit_type="ssh_brute_force"),
                ExploitResult(target=targets[1], success=True, exploit_type="ssh_key_auth")
            ]
            mock_exploit.side_effect = mock_results

            results = await self.exploiter.exploit_targets(targets, config)

            assert len(results) == 2
            assert all(result.success for result in results)

    @pytest.mark.asyncio
    async def test_create_tunnel_success(self):
        """Test successful SSH tunnel creation"""
        # Setup active session
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        mock_client = Mock()
        mock_transport = Mock()
        mock_channel = Mock()

        mock_client.get_transport.return_value = mock_transport
        mock_transport.open_channel.return_value = mock_channel

        creds = SSHCredentials(username="admin", password="password")
        session = SSHSession(target=target, client=mock_client, credentials=creds)

        session_key = "*************:22"
        self.exploiter.active_sessions[session_key] = session

        result = await self.exploiter.create_tunnel(session_key, 8080, "********", 80)

        assert result is True
        assert len(session.tunnels) == 1
        tunnel_info = session.tunnels[0]
        assert tunnel_info['local_port'] == 8080
        assert tunnel_info['remote_host'] == "********"
        assert tunnel_info['remote_port'] == 80

    @pytest.mark.asyncio
    async def test_create_tunnel_no_session(self):
        """Test tunnel creation with no active session"""
        result = await self.exploiter.create_tunnel("nonexistent:22", 8080, "********", 80)
        assert result is False

    @pytest.mark.asyncio
    async def test_harvest_credentials_success(self):
        """Test successful credential harvesting"""
        # Setup active session
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        mock_client = Mock()
        creds = SSHCredentials(username="admin", password="password")
        session = SSHSession(target=target, client=mock_client, credentials=creds)

        session_key = "*************:22"
        self.exploiter.active_sessions[session_key] = session

        # Mock command execution responses
        command_outputs = {
            'find /home -name "*.pub" 2>/dev/null': "ssh-rsa AAAAB3NzaC1yc2E...",
            'cat /etc/shadow 2>/dev/null | head -20': "root:$6$salt$hash...",
            'cat /etc/ssh/sshd_config 2>/dev/null': "Port 22\nProtocol 2",
            'find /home -name ".bash_history" 2>/dev/null -exec tail -50 {} \\;': "ls -la\ncd /tmp"
        }

        with patch.object(self.exploiter, 'execute_command') as mock_exec:
            def mock_command_exec(session_key, command):
                return command_outputs.get(command, "")

            mock_exec.side_effect = mock_command_exec

            credentials = await self.exploiter.harvest_credentials(session_key)

            assert 'ssh_keys' in credentials
            assert 'password_hashes' in credentials
            assert 'config_files' in credentials
            assert 'history_files' in credentials
            assert len(credentials['ssh_keys']) > 0
            assert len(credentials['password_hashes']) > 0

    @pytest.mark.asyncio
    async def test_harvest_credentials_no_session(self):
        """Test credential harvesting with no active session"""
        credentials = await self.exploiter.harvest_credentials("nonexistent:22")
        assert credentials == {}  # Returns empty dict, not None

    @pytest.mark.asyncio
    async def test_stealth_mode_delays(self):
        """Test that stealth mode includes appropriate delays"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        config = ExploitationConfig(
            mode=ExploitationMode.STEALTH,
            timeout=30,
            stealth_delay=0.1  # Short delay for testing
        )

        with patch('asyncio.sleep') as mock_sleep:
            with patch.object(self.exploiter, '_try_ssh_login') as mock_login:
                mock_login.return_value = (False, None)  # All attempts fail

                await self.exploiter._brute_force_attack(target, config)

                # Should have called sleep for stealth delays
                assert mock_sleep.call_count > 0

    def test_session_cleanup_with_tunnels(self):
        """Test session cleanup when tunnels exist"""
        # Setup active session with tunnels
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        mock_client = Mock()
        creds = SSHCredentials(username="admin", password="password")
        session = SSHSession(target=target, client=mock_client, credentials=creds)

        # Add mock tunnels
        mock_channel1 = Mock()
        mock_channel2 = Mock()
        session.tunnels = [
            {'channel': mock_channel1, 'local_port': 8080},
            {'channel': mock_channel2, 'local_port': 8081}
        ]

        session_key = "*************:22"
        self.exploiter.active_sessions[session_key] = session

        result = self.exploiter.close_session(session_key)

        assert result is True
        assert session_key not in self.exploiter.active_sessions
        mock_channel1.close.assert_called_once()
        mock_channel2.close.assert_called_once()
        mock_client.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_concurrent_exploitation_with_semaphore(self):
        """Test concurrent exploitation with semaphore control"""
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)
        semaphore = asyncio.Semaphore(1)

        with patch.object(self.exploiter, 'exploit_single_target') as mock_exploit:
            mock_result = ExploitResult(
                target=target,
                success=True,
                exploit_type="ssh_brute_force"
            )
            mock_exploit.return_value = mock_result

            result = await self.exploiter._exploit_single_target_with_semaphore(
                target, config, semaphore
            )

            assert result.success is True
            mock_exploit.assert_called_once_with(target, config)

    def test_error_handling_in_session_operations(self):
        """Test error handling in various session operations"""
        # Test session closure with exception
        target = ExploitTarget(host="*************", port=22, protocol=ProtocolType.SSH, service_name="ssh")
        mock_client = Mock()
        mock_client.close.side_effect = Exception("Close failed")

        creds = SSHCredentials(username="admin", password="password")
        session = SSHSession(target=target, client=mock_client, credentials=creds)

        session_key = "*************:22"
        self.exploiter.active_sessions[session_key] = session

        # Should handle exception gracefully
        result = self.exploiter.close_session(session_key)
        assert result is False  # Should return False due to exception
